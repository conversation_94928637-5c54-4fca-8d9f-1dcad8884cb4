'use strict';

const co = require('co');
const redis = require('redis');
const wrapper = require('co-redis');
const { DefaultConfig } = require('../config/default');
const REPORT_COUNT_KEY = DefaultConfig.redisKey.REPORT_COUNT_KEY;
// const mysqlInstance = require('../models/mysql').mysqlInstance;

// var redisSettings = {
//   Storage: {
//     host: '***********',
//     port: 6379,
//     auth_pass: 'Xiangqin1993',
//   },
// };
const redisSettings = DefaultConfig.redisSettings;
function Redis() {
  const clientObj = this.initRedis();
  this.clientObj = clientObj;
  const Client = this.clientObj;
  this.test = function (mobile) {
    return co(function* () {
      const key = 'test:' + mobile;
      const value = mobile;
      const rs = yield Client.set(key, value);
      return rs;
    }).catch((err) => {
      console.log(`test:::${err}`);
    });
  };

  this.set = function (key, value, time) {
    return co(function* () {
      const result = yield Client.set(key, value);
      yield Client.expire(key, time);
      return result;
    }).catch((err) => {
      console.log(`set:::${err}`);
    });
  };

  this.expire = function (key, value) {
    return co(function* () {
      const result = yield Client.expire(key, value);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::expire:::${err}`);
    });
  };

  this.get = function (key) {
    return co(function* () {
      const result = yield Client.get(key);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::get:::${err}`);
      return null;
    });
  };

  this.onlySet = function (key, value) {
    return co(function* () {
      const result = yield Client.set(key, value);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::onlySet:::${err}`);
    });
  };

  this.getUnicode = function () {
    var chars = '0123456789abcdefghijklmnopqrstuvwxyz';
    var string_length = 8;
    var randomstring = '';
    for (var x = 0; x < string_length; x++) {
      var letterOrNumber = Math.floor(Math.random() * 2);
      if (letterOrNumber == 0) {
        var newNum = Math.floor(Math.random() * 9);
        randomstring += newNum;
      } else {
        var rnum = Math.floor(Math.random() * chars.length);
        randomstring += chars.substring(rnum, rnum + 1);
      }
    }
    return randomstring;
  };

  //设置session
  this.setSessionToRedis = function (uid) {
    const self = this;
    return co(function* () {
      const thisUnicode = self.getUnicode();
      const theKey = 'STR:52HZ:SESSION:' + thisUnicode,
        theValue = uid + '@52HZ@' + parseInt(+new Date() / 1000);
      const r1 = yield Client.set(theKey, theValue);
      const r2 = yield Client.expire(theKey, 60 * 60 * 24 * 30); //30天有效期
      if (r1 === 'OK' && r2 === 1) {
        return thisUnicode;
      } else {
        return false;
      }
    }).catch((err) => {
      console.log(`REDIS:::setSessionToRedis:::${err}`);
    });
  };

  //获取session
  this.getSessionInfo = function (sid) {
    return co(function* () {
      return yield Client.get(sid);
    }).catch((err) => {
      console.log(`REDIS:::getSessionInfo:::${err}`);
    });
  };

  //删除session
  this.clearSessionInfo = function (sid) {
    return co(function* () {
      let key = 'STR:52HZ:SESSION:';
      return yield Client.del(key + sid);
    }).catch((err) => {
      console.log(`REDIS:::clearSessionInfo:::${err}`);
    });
  };

  //验证码 设置
  this.setVerifyCode = function (mobile, code, time) {
    if (!mobile || !code) return;
    return co(function* () {
      let key = 'HASH:CODE:' + mobile;
      yield Client.hmset(key, ['code', code]);
      yield Client.expire(key, time || 60 * 5);
    }).catch((err) => {
      console.log(`REDIS:::setVerifyCode:::${err}`);
    });
  };

  //验证验证码
  this.getVerifyCode = function (mobile) {
    if (!mobile) return;
    return co(function* () {
      let result = yield Client.hmget('HASH:CODE:' + mobile, ['code']);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::getVerifyCode:::${err}`);
    });
  };

  this.getUserInfoByOpenid = function (openid) {
    return co(function* () {});
  };

  this.lpush = function (key, value) {
    return co(function* () {
      const result = yield Client.lpush(key, value);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::lpush:::${err}`);
    });
  };

  this.rpop = function (key) {
    return co(function* () {
      const result = yield Client.rpop(key);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::rpop:::${err}`);
    });
  };

  this.lrange = function (key, start, end) {
    return co(function* () {
      const result = yield Client.lrange(key, start, end);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::lrange:::${err}`);
    });
  };

  this.setAddWithTime = function (key, value, time, from) {
    const arr = [];
    for (let i = 0; i < value.length; i++) {
      if (value[i] !== 'undefined' && typeof value[i] !== 'undefined' && value[i] !== null && value[i] !== '') {
        arr.push(value[i]);
      }
    }

    if (arr.length == 0) {
      const arr2 = [
        100280, 100162, 100093, 100185, 100166, 100271, 100311, 100314, 100334, 100108, 100348, 100337, 100172, 100175,
        100382, 100388, 100265, 100347, 100370, 100391, 100129, 100408, 100133, 100154, 100137, 100197, 100358, 100430,
        100431, 100428, 100308, 100444, 100426, 100452, 100451, 100384, 100463, 100140, 100468, 100475, 100440, 100460,
        100332, 100333, 100435, 100492, 100495, 100402, 100487, 100479, 100509, 100472, 100511, 100442, 100513,
      ];
      const sarr = getRandomArr(arr2, 10);
      for (let i = 0; i < sarr.length; i++) {
        arr.push(sarr[i]);
      }
    }
    if (!time) return;
    return co(function* () {
      const result = yield Client.sadd(key, arr);
      yield Client.expire(key, time);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::setAddWithTime:::${err}`);
    });
  };

  this.zsetAdd = function (key, score, value) {
    if (!key || !value) return;
    return co(function* () {
      const result = yield Client.zadd(key, score, value);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::zsetAdd:::${err}`);
    });
  };

  this.zsetScoreGet = function (key) {
    return co(function* () {
      const result = yield Client.zcard(key);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::zsetScoreGet:::${err}`);
    });
  };

  this.zsetGet = function (key) {
    return co(function* () {
      const result = yield Client.zrangebyscore(key, '-inf', '+inf');
      return result;
    }).catch((err) => {
      console.log(`REDIS:::zsetGet:::${err}`);
    });
  };

  this.zsetDel = function (key, value) {
    return co(function* () {
      const result = yield Client.zrem(key, value);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::zsetDel:::${err}`);
    });
  };

  this.setAdd = function (key, value) {
    return co(function* () {
      const result = yield Client.sadd(key, value);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::setAdd:::${err}`);
    });
  };

  this.setGet = function (key) {
    return co(function* () {
      const result = yield Client.smembers(key);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::setGet:::${err}`);
    });
  };

  this.setDel = function (key, value) {
    return co(function* () {
      const result = yield Client.srem(key, value);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::setDel:::${err}`);
    });
  };

  this.hashAdd = function (key, value) {
    return co(function* () {
      return yield Client.hmset(key, value);
    }).catch((err) => {
      console.log(`REDIS:::hashAdd:::${err}`);
    });
  };

  this.hashGet = function (key) {
    return co(function* () {
      return yield Client.hgetall(key);
    }).catch((err) => {
      console.log(`REDIS:::hashGet:::${err}`);
    });
  };

  this.hmget = function (key, key2) {
    return co(function* () {
      return yield Client.hmget(key, key2);
    }).catch((err) => {
      console.log(err);
    });
  };

  this.hashDel = function (key) {
    return co(function* () {
      return yield Client.del(key);
    }).catch((err) => {
      console.log(`REDIS:::hashDel:::${err}`);
    });
  };

  this.delKey = function (key) {
    return co(function* () {
      const result = yield Client.del(key);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::delKey:::${err}`);
    });
  };

  this.ttl = function (key) {
    return co(function* () {
      const result = yield Client.ttl(key);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::ttl:::${err}`);
    });
  };

  this.setOceanPage = function (userid, value) {
    return co(function* () {
      const key = 'ocean:page:' + userid;
      const rs = yield Client.set(key, value);
      return rs;
    });
  };

  this.getOceanPage = function (userid) {
    return co(function* () {
      const key = 'ocean:page:' + userid;
      const rs = yield Client.get(key);
      return rs;
    });
  };

  // 更新用户在线状态
  this.updateUserOnlineStatus = function (userId) {
    return co(function* () {
      const key = `user:online:${userId}`;
      const value = Date.now().toString();
      // 设置5分钟过期时间
      const result = yield Client.set(key, value);
      yield Client.expire(key, 300); // 300秒 = 5分钟
      return result;
    }).catch((err) => {
      console.log(`REDIS:::updateUserOnlineStatus:::${err}`);
    });
  };

  // 检查用户是否在线
  this.isUserOnline = function (userId) {
    return co(function* () {
      const key = `user:online:${userId}`;
      const result = yield Client.exists(key);
      return result === 1;
    }).catch((err) => {
      console.log(`REDIS:::isUserOnline:::${err}`);
    });
  };

  this.getUserReportCount = function (userid) {
    return co(function* () {
      let count = yield Client.hget(REPORT_COUNT_KEY, userid);
      if (count === null) {
        const mysqlInstance = require('./mysql').mysqlInstance;
        const reportResult = yield mysqlInstance.getUserReportCountV2(userid);
        if (reportResult.length) {
          count = reportResult[0].count;
        } else {
          count = 0;
        }
        yield Client.hset(REPORT_COUNT_KEY, userid, count);
      }
      return parseInt(count);
    });
  };

  this.addReportCount = function (userid) {
    return co(function* () {
      yield Client.hincrby(REPORT_COUNT_KEY, userid, 1);
    });
  };

  this.keys = function (pattern) {
    return co(function* () {
      const result = yield Client.keys(pattern);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::keys:::${err}`);
      return [];
    });
  };

  // 批量删除keys
  this.delKeys = function (keys) {
    return co(function* () {
      if (!Array.isArray(keys) || keys.length === 0) return 0;
      const result = yield Client.del(keys);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::delKeys:::${err}`);
      return 0;
    });
  };

  // 设置带过期时间的字符串
  this.setWithExpire = function (key, value, expireSeconds) {
    return co(function* () {
      const result = yield Client.set(key, value, 'EX', expireSeconds);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::setWithExpire:::${err}`);
      return null;
    });
  };

  // 获取多个key的值
  this.mget = function (keys) {
    return co(function* () {
      if (!Array.isArray(keys) || keys.length === 0) return [];
      const result = yield Client.mget(keys);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::mget:::${err}`);
      return [];
    });
  };

  // 检查key是否存在
  this.exists = function (key) {
    return co(function* () {
      const result = yield Client.exists(key);
      return result === 1;
    }).catch((err) => {
      console.log(`REDIS:::exists:::${err}`);
      return false;
    });
  };

  // 设置key的过期时间(秒)
  this.expireAt = function (key, timestamp) {
    return co(function* () {
      const result = yield Client.expireat(key, timestamp);
      return result === 1;
    }).catch((err) => {
      console.log(`REDIS:::expireAt:::${err}`);
      return false;
    });
  };

  // 原子递增
  this.incr = function (key) {
    return co(function* () {
      const result = yield Client.incr(key);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::incr:::${err}`);
      return 0;
    });
  };

  // 原子递减
  this.decr = function (key) {
    return co(function* () {
      const result = yield Client.decr(key);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::decr:::${err}`);
      return 0;
    });
  };

  // 批量设置多个key-value
  this.mset = function (keyValues) {
    return co(function* () {
      if (!Array.isArray(keyValues) || keyValues.length === 0) return false;
      const result = yield Client.mset(keyValues);
      return result === 'OK';
    }).catch((err) => {
      console.log(`REDIS:::mset:::${err}`);
      return false;
    });
  };

  // 获取所有匹配pattern的key数量
  this.keysCount = function (pattern) {
    return co(function* () {
      const keys = yield Client.keys(pattern);
      return keys ? keys.length : 0;
    }).catch((err) => {
      console.log(`REDIS:::keysCount:::${err}`);
      return 0;
    });
  };

  // 设置key的值，如果key不存在
  this.setnx = function (key, value) {
    return co(function* () {
      const result = yield Client.setnx(key, value);
      return result === 1;
    }).catch((err) => {
      console.log(`REDIS:::setnx:::${err}`);
      return false;
    });
  };

  // 批量删除匹配pattern的所有key
  this.delByPattern = function (pattern) {
    return co(function* () {
      const keys = yield Client.keys(pattern);
      if (!keys || keys.length === 0) return 0;
      const result = yield Client.del(keys);
      return result;
    }).catch((err) => {
      console.log(`REDIS:::delByPattern:::${err}`);
      return 0;
    });
  };

  // 设置hash的多个字段值
  this.hmsetWithExpire = function (key, hash, expireSeconds) {
    return co(function* () {
      const result = yield Client.hmset(key, hash);
      if (result === 'OK') {
        yield Client.expire(key, expireSeconds);
      }
      return result === 'OK';
    }).catch((err) => {
      console.log(`REDIS:::hmsetWithExpire:::${err}`);
      return false;
    });
  };

  // 获取hash的所有字段和值
  this.hgetallWithTTL = function (key) {
    return co(function* () {
      const [hash, ttl] = yield [Client.hgetall(key), Client.ttl(key)];
      return {
        data: hash,
        ttl: ttl,
      };
    }).catch((err) => {
      console.log(`REDIS:::hgetallWithTTL:::${err}`);
      return { data: null, ttl: -2 };
    });
  };
}

function getRandomArr(arr, num) {
  const len = arr.length;
  for (var i = len - 1; i >= 0; i--) {
    var randomIndex = Math.floor(Math.random() * (i + 1));
    var itemIndex = arr[randomIndex];
    arr[randomIndex] = arr[i];
    arr[i] = itemIndex;
  }
  const tmpArr = arr;
  const arrList = [];
  for (let i = 0; i < num; i++) {
    arrList.push(tmpArr[i]);
  }
  return arrList;
}
Redis.prototype.initRedis = function () {
  return this.createClient();
};

Redis.prototype.createClient = function () {
  // 优化Redis连接配置
  const redisConfig = {
    ...redisSettings.Storage,
    retry_unfulfilled_commands: true,
    retry_delay_on_failover: 100,
    enable_offline_queue: false,
    max_attempts: 3,
    connect_timeout: 60000,
    lazyConnect: true,
    keepAlive: true,
    family: 4, // 4 (IPv4) or 6 (IPv6)
    db: 0,
  };
  return wrapper(redis.createClient(redisConfig));
};

let redisInstance = null;

if (!redisInstance) {
  redisInstance = new Redis();
}

module.exports.redisInstance = redisInstance;
