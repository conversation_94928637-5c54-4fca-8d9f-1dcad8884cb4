[mysqld]
user=mysql
default-storage-engine=INNODB
character-set-client-handshake=FALSE
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

# 从库配置
server-id=2
relay-log=mysql-relay-bin
log-slave-updates=1
read-only=1
replicate-do-db=52hz

# 性能优化
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
slave_parallel_workers=4
slave_parallel_type=LOGICAL_CLOCK

[client]
default-character-set=utf8mb4

[mysql]
default-character-set=utf8mb4
