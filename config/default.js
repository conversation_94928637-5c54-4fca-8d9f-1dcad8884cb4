'use strict';
const DOC_DOMAIN = 'http://doc.52hz.space/';

module.exports.DefaultConfig = {
  redisSettings: {
    Storage: {
      host: '***********',
      port: 6379,
      auth_pass: 'Xiangqin1993',
    },
  },
  mysqlSettings: {
    port: 3306,
    host: '***********',
    user: 'root',
    password: '52hz2023.!@',
    database: '52hz',
  },
  mysqlSettingsDev: {
    port: 3306,
    host: '***********',
    user: 'root',
    password: '52hz2023.!@',
    database: '52hz_dev',
  },
  wailian: {
    IMAGE_DOMAIN: 'http://images.52hz.space/',
    VIDEO_DOMAIN: 'http://video.52hz.space/',
    AVATAR_DOMAIN: 'http://avatar.52hz.space/',
    DOC_DOMAIN: 'http://doc.52hz.space/',
    VOICE_DOMAIN: 'http://voice.52hz.space/',
    IM_DOMAIN: 'http://imimage.52hz.space/',
    APP_DOMAIN: 'https://app.52hz.space',
    OPERATIONS_DOMAIN: 'http://operations.52hz.space/',
  },
  aliyun: {
    accessKeyId: 'LTAI5tRFfTv6AbFnrW2Txiew',
    secretAccessKey: '******************************',
  },
  wechat: {
    appId: 'wx09d09b1ce4d983b0',
    appSecret: '5514e359c53b8bc09688b603ca19636c',
    ak: 'Hw4GOeS3snm6fWkt7q2WcL08o5GYaRz1',
  },
  redis: {
    CHARS: '1234567890',
    CONTENTADD: [0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
    LIKEADD: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
    ALGORITHM: [0, 1],
    LIKE: [0.01, 0.02, 0.03, 0.04, 0.05],
    CONTENT: [
      0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.09, 0.1, 0.11, 0.12, 0.13, 0.14, 0.15, 0.16, 0.17, 0.18, 0.19,
      0.2,
    ],
  },
  LINKSHARE: ['jike', 'weixin', 'douban', 'b23', 'weibo', 'xiaoyuzhou', 'zhihu'],
  LINKSHAREBODY: {
    jike: '即刻',
    weixin: '微信',
    douban: '豆瓣',
    b23: '哔哩哔哩',
    weibo: '微博',
    xiaoyuzhou: '小宇宙',
    zhihu: '知乎',
  },
  LINKSHAREICON: {
    jike: `${DOC_DOMAIN}jike.png`,
    weixin: `${DOC_DOMAIN}wechat.png`,
    douban: `${DOC_DOMAIN}douban.png`,
    b23: `${DOC_DOMAIN}bilibili.png`,
    weibo: `${DOC_DOMAIN}weibo.png`,
    xiaoyuzhou: `${DOC_DOMAIN}xiaoyouzhou.png`,
    zhihu: `${DOC_DOMAIN}zhihu.png`,
  },
  sig_config: {
    sdk_appid: 1400154642,
    expire_after: 180 * 24 * 3600,
    private_key: '../../private_key',
    public_key: '../../public_key',
  },
  blackUserArr: [101167, 105036, 105065, 105124, 105091, 105068, 107793, 109188],
  logUserid: [7, 100008, 108474, 120425],
  recommenArr: [7, 11, 101678, 100008],
  // mobileArr: [
  //   15705271152, 17625930503, 19931002053, 12345678910, 15148138849, 11111111111, 22222222222, 33333333333, 44444444444,
  //   55555555555, 66666666666, 77777777777, 88888888888, 99999999999, 13888888888, 13777777777, 13111111111, 13222222222,
  //   13333333333, 13444444444, 13555555555, 13666666666, 13999999999, 15888888888, 15111111111, 15222222222, 15333333333,
  //   15444444444, 15555555555, 15666666666, 15777777777, 12987654321, 12111111111, 12222222222, 12333333333, 12444444444,
  //   12555555555, 19936200034, 19123419192,
  // ],
  mobileArr: [
    15705271152, 17625930503, 19931002053, 15148138849, 19936200034, 11157834629, 11386742095, 11748563901, 11625891470,
    11817263509, 11302817465, 11475893210, 11163849520, 11234507891, 11512378460,
  ],
  myInv: [462013],
  uidMapping: {
    19930503: 7,
    19930620: 100008,
    19931002: 108474,
  },
  sms: {
    accessKeyId: 'LTAI5tRFfTv6AbFnrW2Txiew',
    secretAccessKey: '******************************',
  },
  reportType: {
    1: '对话令我反感',
    2: '色情骚扰',
    3: '垃圾广告',
    4: '不友善 / 恶意辱骂',
    5: '违法有害',
    6: '提供色情服务',
  },
  qiniu: {
    accessKey: 'O44_RrkHRd0INPkjXg8iwp04O_TnCeEugnbIzu8S',
    secretKey: 'zrVaJuTjip9NtwCOpsGULyFIw1ZR6BgW6uSm-lGf',
  },
  crypto: {
    key: '52HERTZ123456ZGX',
    iv: 'ZGX12345652HERTZ',
  },
  redisKey: {
    REPORT_COUNT_KEY: 'user:report:count',
  },
  YEAR_BADGES: {
    1577808000: {
      type: 4,
      type_name: '领队小鲸鱼',
      introduction: '2019年注册用户',
      icon: `${DOC_DOMAIN}oneyear.png`,
    },
    // 1609430400: {
    //   type: 5,
    //   type_name: '周岁小鲸鱼',
    //   introduction: '2020年注册用户',
    //   icon: `${DOC_DOMAIN}2019年注册.png`,
    // },
  },
  CACHE_CONFIG: {
    enabled: true, // 是否启用缓存
    expire: 3600, // 默认缓存时间(秒) - 1小时
    prefix: 'SQL_CACHE:', // 缓存key前缀
    // 不同类型查询的缓存时间配置
    cacheExpires: {
      user: 1800, // 用户信息缓存30分钟
      content: 600, // 内容信息缓存10分钟
      attention: 300, // 关注信息缓存5分钟
      like: 60, // 点赞信息缓存1分钟
      comment: 60, // 评论信息缓存1分钟
      tag: 7200, // 标签信息缓存2小时
      config: 86400, // 配置信息缓存24小时
    },
  },
};
