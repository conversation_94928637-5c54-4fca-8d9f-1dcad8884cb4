[mysqld]
user=mysql
default-storage-engine=INNODB
character-set-client-handshake=FALSE
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

# 主库配置
server-id=1
log-bin=mysql-bin
binlog-format=ROW
binlog-do-db=52hz
expire_logs_days=7
max_binlog_size=100M

# 性能优化
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
innodb_flush_log_at_trx_commit=1
sync_binlog=1

[client]
default-character-set=utf8mb4

[mysql]
default-character-set=utf8mb4
